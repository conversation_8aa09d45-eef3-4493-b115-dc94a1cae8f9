"use client";

import React from "react";
import { StickyHeroSection } from "../home/<USER>";

export function StickyHeroExample() {
  const handleButton1Click = () => {
    console.log("Button 1 clicked!");
    // Add your action here
  };

  const handleButton2Click = () => {
    console.log("Button 2 clicked!");
    // Add your action here
  };

  return (
    <div>
      <StickyHeroSection
        backgroundImage="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=2071&auto=format&fit=crop"
        backgroundAlt="Beautiful forest landscape"
        mainTitle="Transform Your Vision Into Reality"
        button1Text="Get Started"
        button2Text="Learn More"
        secondaryTitle="Building Sustainable Solutions"
        description="We believe in creating meaningful impact through innovative approaches. Our team works closely with organizations to develop sustainable solutions that drive real change. From concept to implementation, we're committed to excellence and environmental responsibility."
        onButton1Click={handleButton1Click}
        onButton2Click={handleButton2Click}
      />
      
      {/* Additional content below the sticky section */}
      <section className="py-16 px-[5%] bg-background">
        <div className="container mx-auto">
          <h3 className="text-3xl font-bold text-text-primary mb-6">
            Content After Sticky Section
          </h3>
          <p className="text-text-secondary max-w-3xl">
            This content appears after the sticky hero section. The sticky background image 
            will remain in place until all the text content has scrolled past, creating a 
            beautiful parallax-like effect.
          </p>
        </div>
      </section>
    </div>
  );
}

// Usage in a page:
// import { StickyHeroExample } from './_components/examples/StickyHeroExample';
// 
// export default function Page() {
//   return <StickyHeroExample />;
// }
