"use client";

import React from "react";

export function StickyHeroSection({
  backgroundImage = "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=2071&auto=format&fit=crop",
  backgroundAlt = "Background image",
  mainTitle = "Your Main Title Here",
  button1Text = "Action 1",
  button2Text = "Action 2",
  secondaryTitle = "Secondary Title",
  description = "Your description text goes here. This paragraph will be aligned to the right and scroll over the sticky background image.",
  onButton1Click = () => {},
  onButton2Click = () => {},
  className = ""
}) {
  return (
    <section className={`relative ${className}`}>
      <div className="px-[5%]">
        <div className="relative z-10 container">
          <div className="grid auto-cols-fr grid-cols-1 pb-8">
            
            {/* Main content section - left aligned */}
            <div className="relative">
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-2xl">
                  <h1 className="mb-5 text-6xl leading-[1.2] font-semibold text-text-alternative md:mb-6 md:text-5xl lg:text-8xl">
                    {mainTitle}
                  </h1>
                  <div className="flex flex-row gap-4">
                    <button 
                      onClick={onButton1Click}
                      className="px-6 py-3 bg-link text-text-alternative rounded-lg hover:bg-link-primary transition-colors font-medium"
                    >
                      {button1Text}
                    </button>
                    <button 
                      onClick={onButton2Click}
                      className="px-6 py-3 bg-background-secondary text-text-primary border border-border-primary rounded-lg hover:bg-background-tertiary transition-colors font-medium"
                    >
                      {button2Text}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Spacer for sticky image */}
            <div className="static top-0 order-first flex h-[50vh] items-center justify-center md:sticky md:order-none md:h-screen">
              {/* This div creates space for the sticky background */}
            </div>

            {/* Secondary content section - right aligned */}
            <div className="relative md:pt-[150vh]">
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-2xl ml-auto">
                  <h2 className="mb-5 text-4xl leading-[1.2] font-semibold text-text-alternative md:mb-6 md:text-5xl lg:text-6xl">
                    {secondaryTitle}
                  </h2>
                  <p className="text-text-alternative md:text-lg leading-relaxed">
                    {description}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Bottom spacer to ensure proper scroll behavior */}
          <div className="mb-[-100vh]" />
        </div>
      </div>

      {/* Sticky background image */}
      <div className="sticky bottom-0 z-0 h-screen w-screen">
        <div className="absolute inset-0 z-10 bg-black/50" />
        <div className="sticky bottom-0 h-screen w-screen overflow-hidden">
          <img
            src={backgroundImage}
            alt={backgroundAlt}
            className="absolute -top-full -right-full -bottom-full -left-full m-auto size-full object-cover"
          />
        </div>
      </div>
    </section>
  );
}
